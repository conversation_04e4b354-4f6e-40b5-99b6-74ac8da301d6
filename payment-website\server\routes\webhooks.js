import express from 'express';
import { verifyWebhookSignature } from '../config/razorpay.js';
// import { pool } from '../config/database.js'; // Temporarily disabled
import { validateWebhookData } from '../middleware/validation.js';
// import {
//   sendPaymentSuccessNotifications,
//   sendPaymentFailureNotifications
// } from '../services/notificationService.js'; // Temporarily disabled

const router = express.Router();

// Razorpay webhook endpoint
router.post('/razorpay', validateWebhookData, async (req, res) => {
  try {
    const signature = req.webhookSignature;
    const body = req.body;

    // Verify webhook signature
    const isValid = verifyWebhookSignature(body, signature);
    if (!isValid) {
      console.log('❌ Invalid webhook signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    console.log('📨 Webhook received:', body.event);
    console.log('📨 Webhook payload:', JSON.stringify(body, null, 2));

    // Handle different webhook events (logging only for now)
    switch (body.event) {
      case 'payment.captured':
        console.log('✅ Payment captured:', body.payload.payment.entity.id);
        break;

      case 'payment.failed':
        console.log('❌ Payment failed:', body.payload.payment.entity.id);
        break;

      case 'order.paid':
        console.log('✅ Order paid:', body.payload.order.entity.id);
        break;

      default:
        console.log(`ℹ️ Unhandled webhook event: ${body.event}`);
    }

    res.status(200).json({
      success: true,
      message: 'Webhook received and logged',
      event: body.event
    });

  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Webhook processing failed'
    });
  }
});

// Database functions temporarily disabled - will be restored when PostgreSQL is running

// Health check for webhooks
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Webhook endpoint is healthy',
    timestamp: new Date().toISOString()
  });
});

export default router;
