import express from 'express';
import { verifyWebhookSignature } from '../config/razorpay.js';
import { pool } from '../config/database.js';
import { validateWebhookData } from '../middleware/validation.js';
import {
  sendPaymentSuccessNotifications,
  sendPaymentFailureNotifications
} from '../services/notificationService.js';

const router = express.Router();

// Razorpay webhook endpoint
router.post('/razorpay', validateWebhookData, async (req, res) => {
  const client = await pool.connect();

  try {
    const signature = req.webhookSignature;
    const body = req.body;

    // Verify webhook signature
    const isValid = verifyWebhookSignature(body, signature);
    if (!isValid) {
      console.log('❌ Invalid webhook signature');
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    console.log('📨 Webhook received:', body.event);

    await client.query('BEGIN');

    // Handle different webhook events
    switch (body.event) {
      case 'payment.captured':
        await handlePaymentCaptured(client, body.payload.payment.entity);
        break;

      case 'payment.failed':
        await handlePaymentFailed(client, body.payload.payment.entity);
        break;

      case 'order.paid':
        await handleOrderPaid(client, body.payload.order.entity);
        break;

      default:
        console.log(`ℹ️ Unhandled webhook event: ${body.event}`);
    }

    // Store webhook data for debugging
    await client.query(`
      UPDATE payments
      SET webhook_data = COALESCE(webhook_data, '[]'::jsonb) || $1::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE razorpay_order_id = $2
    `, [JSON.stringify([body]), body.payload.payment?.entity?.order_id || body.payload.order?.entity?.id]);

    await client.query('COMMIT');

    res.status(200).json({ success: true });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Webhook processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Webhook processing failed'
    });
  } finally {
    client.release();
  }
});

// Handle payment captured event
async function handlePaymentCaptured(client, payment) {
  console.log('✅ Processing payment.captured:', payment.id);

  try {
    // Update payment status
    const paymentResult = await client.query(`
      UPDATE payments
      SET status = 'captured',
          razorpay_payment_id = $1,
          payment_method = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE razorpay_order_id = $3
      RETURNING order_id
    `, [payment.id, payment.method, payment.order_id]);

    if (paymentResult.rows.length > 0) {
      const orderId = paymentResult.rows[0].order_id;

      // Update order status
      await client.query(`
        UPDATE orders
        SET status = 'paid', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [orderId]);

      console.log('✅ Payment captured and order updated:', orderId);

      // Send payment success notifications (async, don't wait)
      setImmediate(async () => {
        try {
          await sendPaymentSuccessNotifications(orderId);
        } catch (error) {
          console.error('❌ Failed to send payment success notifications:', error);
        }
      });
    }
  } catch (error) {
    console.error('❌ Error handling payment.captured:', error);
    throw error;
  }
}

// Handle payment failed event
async function handlePaymentFailed(client, payment) {
  console.log('❌ Processing payment.failed:', payment.id);

  try {
    // Update payment status
    const paymentResult = await client.query(`
      UPDATE payments
      SET status = 'failed',
          razorpay_payment_id = $1,
          payment_method = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE razorpay_order_id = $3
      RETURNING order_id
    `, [payment.id, payment.method, payment.order_id]);

    if (paymentResult.rows.length > 0) {
      const orderId = paymentResult.rows[0].order_id;

      // Update order status
      await client.query(`
        UPDATE orders
        SET status = 'failed', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [orderId]);

      console.log('❌ Payment failed and order updated:', orderId);

      // Send payment failure notifications (async, don't wait)
      setImmediate(async () => {
        try {
          await sendPaymentFailureNotifications(orderId);
        } catch (error) {
          console.error('❌ Failed to send payment failure notifications:', error);
        }
      });
    }
  } catch (error) {
    console.error('❌ Error handling payment.failed:', error);
    throw error;
  }
}

// Handle order paid event
async function handleOrderPaid(client, order) {
  console.log('✅ Processing order.paid:', order.id);

  try {
    // Update order status if not already updated
    await client.query(`
      UPDATE orders
      SET status = 'paid', updated_at = CURRENT_TIMESTAMP
      WHERE id = (
        SELECT order_id FROM payments WHERE razorpay_order_id = $1
      ) AND status != 'paid'
    `, [order.id]);

    console.log('✅ Order paid status updated:', order.id);
  } catch (error) {
    console.error('❌ Error handling order.paid:', error);
    throw error;
  }
}

// Health check for webhooks
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Webhook endpoint is healthy',
    timestamp: new Date().toISOString()
  });
});

export default router;
