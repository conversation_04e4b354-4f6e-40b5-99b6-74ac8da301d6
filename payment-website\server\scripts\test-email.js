import { testEmailConfiguration } from '../services/emailService.js';
import dotenv from 'dotenv';

dotenv.config();

async function testEmail() {
  console.log('🧪 Testing email configuration...');
  console.log('📧 Email Service:', process.env.EMAIL_SERVICE || 'Not configured');
  console.log('👤 Email User:', process.env.EMAIL_USER || 'Not configured');
  console.log('🔔 Notifications Enabled:', process.env.ENABLE_EMAIL_NOTIFICATIONS || 'false');
  
  try {
    const result = await testEmailConfiguration();
    
    if (result.success) {
      console.log('✅ Email configuration test passed!');
      console.log('📨 You can now send payment confirmation emails.');
    } else {
      console.log('❌ Email configuration test failed:');
      console.log('Error:', result.error);
      console.log('\n📋 Troubleshooting steps:');
      console.log('1. Check your EMAIL_USER and EMAIL_PASSWORD in .env');
      console.log('2. For Gmail, use an App Password instead of your regular password');
      console.log('3. Enable 2-Factor Authentication and generate an App Password');
      console.log('4. Make sure EMAIL_SERVICE is set to "gmail" for Gmail accounts');
    }
  } catch (error) {
    console.log('❌ Email test failed with error:', error.message);
  }
}

testEmail();
