import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import paymentRoutes from './routes/payment.js';
import orderRoutes from './routes/orders.js';
import webhookRoutes from './routes/webhooks.js';
import { initializeDatabase } from './config/database.js';

// Load environment variables
dotenv.config({ path: './server/.env' });

// Debug: Log environment variables
console.log('🔍 Environment variables loaded:');
console.log('RAZORPAY_KEY_ID:', process.env.RAZORPAY_KEY_ID ? 'Set' : 'Not set');
console.log('RAZORPAY_KEY_SECRET:', process.env.RAZORPAY_KEY_SECRET ? 'Set' : 'Not set');
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? 'Set' : 'Not set');

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for ngrok and other reverse proxies
app.set('trust proxy', true);

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'PayShop Server is running',
    timestamp: new Date().toISOString()
  });
});

// Test webhook endpoint directly
app.get('/api/webhooks/test', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Direct webhook test endpoint working',
    timestamp: new Date().toISOString()
  });
});

// Debug: Log route registration
console.log('🔧 Registering routes...');

// API routes
app.use('/api/payments', paymentRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/webhooks', webhookRoutes);

console.log('✅ Routes registered successfully');

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize database and start server
async function startServer() {
  try {
    // Skip database for now - focus on webhook setup
    // await initializeDatabase();
    // console.log('✅ Database initialized successfully');
    console.log('⚠️ Skipping database - focusing on webhook setup');

    app.listen(PORT, () => {
      console.log(`🚀 PayShop Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Webhook endpoint: http://localhost:${PORT}/api/webhooks/razorpay`);
      console.log(`🔗 Ready for ngrok setup!`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
