@echo off
echo Installing PayShop Backend Dependencies...
echo.

echo Installing Node.js dependencies...
npm install

echo.
echo Dependencies installed successfully!
echo.
echo Next steps:
echo 1. Copy .env.example to .env and configure your settings
echo 2. Set up PostgreSQL database
echo 3. Configure Razorpay credentials
echo 4. Configure email settings (optional)
echo 5. Run: npm run dev
echo.
echo For email testing, run: node scripts/test-email.js
echo.
pause
