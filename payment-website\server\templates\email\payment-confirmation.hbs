<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4CAF50;
            margin: 0;
            font-size: 28px;
        }
        .success-icon {
            font-size: 48px;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        .order-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #4CAF50;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
        }
        .contact-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✅</div>
            <h1>Payment Confirmed!</h1>
            <p>Thank you for your purchase, {{customerName}}</p>
        </div>

        <div class="order-details">
            <h3>Order Details</h3>
            <div class="detail-row">
                <span>Order Number:</span>
                <span><strong>{{orderNumber}}</strong></span>
            </div>
            <div class="detail-row">
                <span>Payment ID:</span>
                <span>{{paymentId}}</span>
            </div>
            <div class="detail-row">
                <span>Payment Method:</span>
                <span>{{paymentMethod}}</span>
            </div>
            <div class="detail-row">
                <span>Order Date:</span>
                <span>{{orderDate}}</span>
            </div>
            <div class="detail-row">
                <span>Total Amount:</span>
                <span>{{currency}} {{totalAmount}}</span>
            </div>
        </div>

        {{#if items}}
        <h3>Items Purchased</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{this.name}}</td>
                    <td>{{this.quantity}}</td>
                    <td>₹{{this.price}}</td>
                    <td>₹{{multiply this.price this.quantity}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
        {{/if}}

        <div class="contact-info">
            <h4>Need Help?</h4>
            <p>If you have any questions about your order, please contact us:</p>
            <p>
                📧 Email: {{companyEmail}}<br>
                📞 Phone: {{companyPhone}}
            </p>
        </div>

        <div class="footer">
            <p>This is an automated email from {{companyName}}. Please do not reply to this email.</p>
            <p>© {{year}} {{companyName}}. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Helper function for Handlebars
        Handlebars.registerHelper('multiply', function(a, b) {
            return a * b;
        });
        
        Handlebars.registerHelper('year', function() {
            return new Date().getFullYear();
        });
    </script>
</body>
</html>
