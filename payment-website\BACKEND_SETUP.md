# PayShop Backend Setup Guide

## 🎯 Overview

Your PayShop backend now includes a comprehensive payment gateway with advanced payment confirmation features:

- ✅ **Razorpay Integration** - Complete payment processing
- ✅ **PostgreSQL Database** - Robust data storage
- ✅ **Email Notifications** - Payment confirmations and receipts
- ✅ **SMS Support** - Optional SMS notifications
- ✅ **Webhook Processing** - Real-time payment updates
- ✅ **QR Code Generation** - UPI payment QR codes
- ✅ **Security Features** - Rate limiting, validation, CORS

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd payment-website/server
npm install
```

Or run the batch file:
```bash
install-dependencies.bat
```

### 2. Configure Environment

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` with your settings:

```env
# Database (Required)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=payshop_db
DB_USER=postgres
DB_PASSWORD=your_postgres_password

# Razorpay (Required)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Email (Optional but recommended)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
ENABLE_EMAIL_NOTIFICATIONS=true
```

### 3. Set Up PostgreSQL

Make sure PostgreSQL is installed and running, then create the database:

```sql
CREATE DATABASE payshop_db;
```

### 4. Start the Server

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3001`

## 🔧 Detailed Configuration

### PostgreSQL Setup

1. **Install PostgreSQL** (if not already installed)
   - Download from: https://www.postgresql.org/download/
   - Or use your package manager

2. **Create Database**
   ```bash
   # Connect to PostgreSQL
   psql -U postgres
   
   # Create database
   CREATE DATABASE payshop_db;
   
   # Exit
   \q
   ```

3. **Update .env file** with your PostgreSQL credentials

### Razorpay Setup

1. **Create Razorpay Account**
   - Go to https://razorpay.com
   - Sign up for a free account

2. **Get API Keys**
   - Dashboard → Settings → API Keys
   - Copy Key ID and Key Secret

3. **Set Up Webhooks**
   - Dashboard → Settings → Webhooks
   - Add webhook URL: `https://yourdomain.com/api/webhooks/razorpay`
   - Select events: `payment.captured`, `payment.failed`, `order.paid`
   - Copy webhook secret

### Email Configuration (Gmail)

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Security → 2-Step Verification

2. **Generate App Password**
   - Security → 2-Step Verification → App passwords
   - Select "Mail" and generate password
   - Use this password in `EMAIL_PASSWORD`

3. **Test Email Configuration**
   ```bash
   npm run test-email
   ```

## 📊 Database Schema

The system automatically creates these tables:

- **customers** - Customer information
- **orders** - Order details and status
- **payments** - Payment transactions
- **notification_logs** - Notification audit trail

## 🧪 Testing the Backend

### 1. Health Check
```bash
curl http://localhost:3001/health
```

### 2. Test Email Configuration
```bash
npm run test-email
```

### 3. Create Test Payment Order
```bash
curl -X POST http://localhost:3001/api/payments/create-order \
  -H "Content-Type: application/json" \
  -d '{
    "customerData": {
      "firstName": "Test",
      "lastName": "User",
      "email": "<EMAIL>",
      "phone": "**********",
      "address": "123 Test Street",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400001"
    },
    "orderData": {
      "amount": 100.00
    },
    "items": [
      {
        "id": 1,
        "name": "Test Product",
        "price": 100.00,
        "quantity": 1
      }
    ]
  }'
```

## 🔔 Notification Features

### Email Notifications

The system sends professional HTML emails for:
- ✅ Payment confirmations
- ❌ Payment failures
- 🧾 Order receipts

### SMS Notifications (Optional)

Configure SMS provider in the notification service to enable SMS alerts.

### Webhook Notifications

Real-time payment status updates from Razorpay are processed automatically.

## 🛡️ Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Comprehensive data validation
- **CORS Protection**: Configurable cross-origin policies
- **SQL Injection Protection**: Parameterized queries
- **Secure Headers**: Helmet.js security middleware

## 📈 API Endpoints

### Payment APIs
- `POST /api/payments/create-order` - Create payment order
- `POST /api/payments/verify` - Verify payment
- `GET /api/payments/status/:orderId` - Get payment status
- `POST /api/payments/send-receipt/:orderId` - Send receipt
- `POST /api/payments/retry-notifications/:orderId` - Retry notifications

### Order APIs
- `GET /api/orders/:orderId` - Get order details
- `GET /api/orders/customer/:email` - Get customer orders
- `PATCH /api/orders/:orderId/status` - Update order status
- `GET /api/orders/stats/summary` - Order statistics

### Webhook APIs
- `POST /api/webhooks/razorpay` - Razorpay webhook endpoint
- `GET /api/webhooks/health` - Webhook health check

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify database credentials in .env
   - Ensure database exists

2. **Email Not Sending**
   - Run `npm run test-email` to diagnose
   - Check Gmail App Password setup
   - Verify EMAIL_USER and EMAIL_PASSWORD

3. **Razorpay Errors**
   - Verify API keys are correct
   - Check webhook URL is accessible
   - Ensure webhook secret matches

4. **Port Already in Use**
   - Change PORT in .env file
   - Kill existing process: `lsof -ti:3001 | xargs kill`

### Logs and Debugging

- All activities are logged to console
- Payment events include detailed timestamps
- Error logs include stack traces in development mode

## 🔄 Next Steps

1. **Frontend Integration**: Connect your React frontend to these APIs
2. **Production Deployment**: Deploy to your preferred hosting platform
3. **Monitoring**: Set up logging and monitoring tools
4. **Backup**: Configure database backups
5. **SSL**: Enable HTTPS for production

## 📞 Support

If you encounter any issues:
1. Check the logs in the terminal
2. Verify all environment variables are set
3. Test individual components (database, email, Razorpay)
4. Review the API documentation in `/server/README.md`

Your payment gateway backend is now ready for production use! 🎉
