import nodemailer from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create email transporter
const createTransporter = () => {
  const config = {
    service: process.env.EMAIL_SERVICE || 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    }
  };

  // For development, you can use Ethereal Email for testing
  if (process.env.NODE_ENV === 'development' && !process.env.EMAIL_USER) {
    console.log('⚠️ Email service not configured. Using console logging for emails.');
    return null;
  }

  return nodemailer.createTransporter(config);
};

// Load and compile email templates
const loadTemplate = (templateName) => {
  try {
    const templatePath = path.join(__dirname, '../templates/email', `${templateName}.hbs`);
    const templateSource = fs.readFileSync(templatePath, 'utf8');
    return handlebars.compile(templateSource);
  } catch (error) {
    console.error(`Failed to load email template ${templateName}:`, error);
    return null;
  }
};

// Send payment confirmation email
export async function sendPaymentConfirmationEmail(orderData, customerData, paymentData) {
  try {
    if (!process.env.ENABLE_EMAIL_NOTIFICATIONS || process.env.ENABLE_EMAIL_NOTIFICATIONS !== 'true') {
      console.log('📧 Email notifications disabled');
      return { success: true, message: 'Email notifications disabled' };
    }

    const transporter = createTransporter();
    if (!transporter) {
      console.log('📧 Payment confirmation email (console):', {
        to: customerData.email,
        subject: `Payment Confirmation - Order ${orderData.orderNumber}`,
        orderNumber: orderData.orderNumber,
        amount: orderData.totalAmount,
        paymentId: paymentData.razorpayPaymentId
      });
      return { success: true, message: 'Email logged to console' };
    }

    const template = loadTemplate('payment-confirmation');
    if (!template) {
      throw new Error('Payment confirmation template not found');
    }

    const emailData = {
      customerName: `${customerData.firstName} ${customerData.lastName}`,
      orderNumber: orderData.orderNumber,
      totalAmount: orderData.totalAmount,
      currency: orderData.currency || 'INR',
      paymentId: paymentData.razorpayPaymentId,
      paymentMethod: paymentData.method,
      orderDate: new Date(orderData.createdAt).toLocaleDateString('en-IN'),
      items: orderData.items,
      companyName: process.env.COMPANY_NAME || 'PayShop',
      companyEmail: process.env.COMPANY_EMAIL || '<EMAIL>',
      companyPhone: process.env.COMPANY_PHONE || '+91-9876543210'
    };

    const htmlContent = template(emailData);

    const mailOptions = {
      from: process.env.EMAIL_FROM || 'PayShop <<EMAIL>>',
      to: customerData.email,
      subject: `Payment Confirmation - Order ${orderData.orderNumber}`,
      html: htmlContent,
      text: `Dear ${emailData.customerName},\n\nYour payment for order ${orderData.orderNumber} has been confirmed.\n\nAmount: ₹${orderData.totalAmount}\nPayment ID: ${paymentData.razorpayPaymentId}\n\nThank you for your purchase!\n\nBest regards,\n${emailData.companyName}`
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Payment confirmation email sent:', result.messageId);
    
    return { 
      success: true, 
      messageId: result.messageId,
      message: 'Payment confirmation email sent successfully' 
    };

  } catch (error) {
    console.error('❌ Failed to send payment confirmation email:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
}

// Send payment failed email
export async function sendPaymentFailedEmail(orderData, customerData, paymentData) {
  try {
    if (!process.env.ENABLE_EMAIL_NOTIFICATIONS || process.env.ENABLE_EMAIL_NOTIFICATIONS !== 'true') {
      return { success: true, message: 'Email notifications disabled' };
    }

    const transporter = createTransporter();
    if (!transporter) {
      console.log('📧 Payment failed email (console):', {
        to: customerData.email,
        subject: `Payment Failed - Order ${orderData.orderNumber}`,
        orderNumber: orderData.orderNumber
      });
      return { success: true, message: 'Email logged to console' };
    }

    const template = loadTemplate('payment-failed');
    if (!template) {
      throw new Error('Payment failed template not found');
    }

    const emailData = {
      customerName: `${customerData.firstName} ${customerData.lastName}`,
      orderNumber: orderData.orderNumber,
      totalAmount: orderData.totalAmount,
      currency: orderData.currency || 'INR',
      companyName: process.env.COMPANY_NAME || 'PayShop',
      companyEmail: process.env.COMPANY_EMAIL || '<EMAIL>',
      companyPhone: process.env.COMPANY_PHONE || '+91-9876543210'
    };

    const htmlContent = template(emailData);

    const mailOptions = {
      from: process.env.EMAIL_FROM || 'PayShop <<EMAIL>>',
      to: customerData.email,
      subject: `Payment Failed - Order ${orderData.orderNumber}`,
      html: htmlContent,
      text: `Dear ${emailData.customerName},\n\nWe're sorry, but your payment for order ${orderData.orderNumber} could not be processed.\n\nAmount: ₹${orderData.totalAmount}\n\nPlease try again or contact our support team.\n\nBest regards,\n${emailData.companyName}`
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Payment failed email sent:', result.messageId);
    
    return { 
      success: true, 
      messageId: result.messageId,
      message: 'Payment failed email sent successfully' 
    };

  } catch (error) {
    console.error('❌ Failed to send payment failed email:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
}

// Send order receipt email
export async function sendOrderReceiptEmail(orderData, customerData, paymentData) {
  try {
    if (!process.env.ENABLE_EMAIL_NOTIFICATIONS || process.env.ENABLE_EMAIL_NOTIFICATIONS !== 'true') {
      return { success: true, message: 'Email notifications disabled' };
    }

    const transporter = createTransporter();
    if (!transporter) {
      console.log('📧 Order receipt email (console):', {
        to: customerData.email,
        subject: `Order Receipt - ${orderData.orderNumber}`,
        orderNumber: orderData.orderNumber
      });
      return { success: true, message: 'Email logged to console' };
    }

    const template = loadTemplate('order-receipt');
    if (!template) {
      throw new Error('Order receipt template not found');
    }

    const emailData = {
      customerName: `${customerData.firstName} ${customerData.lastName}`,
      orderNumber: orderData.orderNumber,
      totalAmount: orderData.totalAmount,
      currency: orderData.currency || 'INR',
      paymentId: paymentData.razorpayPaymentId,
      paymentMethod: paymentData.method,
      orderDate: new Date(orderData.createdAt).toLocaleDateString('en-IN'),
      items: orderData.items,
      customerAddress: {
        address: customerData.address,
        city: customerData.city,
        state: customerData.state,
        pincode: customerData.pincode
      },
      companyName: process.env.COMPANY_NAME || 'PayShop',
      companyEmail: process.env.COMPANY_EMAIL || '<EMAIL>',
      companyPhone: process.env.COMPANY_PHONE || '+91-9876543210',
      companyAddress: process.env.COMPANY_ADDRESS || '123 Business Street, Mumbai, Maharashtra 400001'
    };

    const htmlContent = template(emailData);

    const mailOptions = {
      from: process.env.EMAIL_FROM || 'PayShop <<EMAIL>>',
      to: customerData.email,
      subject: `Order Receipt - ${orderData.orderNumber}`,
      html: htmlContent
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Order receipt email sent:', result.messageId);
    
    return { 
      success: true, 
      messageId: result.messageId,
      message: 'Order receipt email sent successfully' 
    };

  } catch (error) {
    console.error('❌ Failed to send order receipt email:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
}

// Test email configuration
export async function testEmailConfiguration() {
  try {
    const transporter = createTransporter();
    if (!transporter) {
      return { success: false, message: 'Email service not configured' };
    }

    await transporter.verify();
    console.log('✅ Email configuration is valid');
    return { success: true, message: 'Email configuration is valid' };
  } catch (error) {
    console.error('❌ Email configuration test failed:', error);
    return { success: false, error: error.message };
  }
}

export default {
  sendPaymentConfirmationEmail,
  sendPaymentFailedEmail,
  sendOrderReceiptEmail,
  testEmailConfiguration
};
