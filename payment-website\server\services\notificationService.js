import { 
  sendPaymentConfirmationEmail, 
  sendPaymentFailedEmail, 
  sendOrderReceiptEmail 
} from './emailService.js';
import { pool } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

// Send SMS notification (placeholder - integrate with your SMS provider)
async function sendSMSNotification(phone, message) {
  try {
    if (!process.env.ENABLE_SMS_NOTIFICATIONS || process.env.ENABLE_SMS_NOTIFICATIONS !== 'true') {
      console.log('📱 SMS notifications disabled');
      return { success: true, message: 'SMS notifications disabled' };
    }

    // Placeholder for SMS integration
    // You can integrate with services like Twilio, AWS SNS, or Indian SMS providers
    console.log('📱 SMS notification (console):', {
      to: phone,
      message: message
    });

    return { success: true, message: 'SMS logged to console' };
  } catch (error) {
    console.error('❌ Failed to send SMS:', error);
    return { success: false, error: error.message };
  }
}

// Send webhook notification to external systems
async function sendWebhookNotification(webhookUrl, data) {
  try {
    if (!process.env.ENABLE_WEBHOOK_NOTIFICATIONS || process.env.ENABLE_WEBHOOK_NOTIFICATIONS !== 'true') {
      console.log('🔗 Webhook notifications disabled');
      return { success: true, message: 'Webhook notifications disabled' };
    }

    // Placeholder for webhook integration
    console.log('🔗 Webhook notification (console):', {
      url: webhookUrl,
      data: data
    });

    return { success: true, message: 'Webhook logged to console' };
  } catch (error) {
    console.error('❌ Failed to send webhook:', error);
    return { success: false, error: error.message };
  }
}

// Get complete order data for notifications
async function getOrderDataForNotification(orderId) {
  try {
    const result = await pool.query(`
      SELECT 
        o.id,
        o.order_number,
        o.total_amount,
        o.currency,
        o.status as order_status,
        o.items,
        o.created_at,
        c.first_name,
        c.last_name,
        c.email,
        c.phone,
        c.address,
        c.city,
        c.state,
        c.pincode,
        p.razorpay_order_id,
        p.razorpay_payment_id,
        p.status as payment_status,
        p.payment_method
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN payments p ON o.id = p.order_id
      WHERE o.id = $1
    `, [orderId]);

    if (result.rows.length === 0) {
      throw new Error('Order not found');
    }

    const row = result.rows[0];
    
    return {
      order: {
        id: row.id,
        orderNumber: row.order_number,
        totalAmount: parseFloat(row.total_amount),
        currency: row.currency,
        status: row.order_status,
        items: row.items,
        createdAt: row.created_at
      },
      customer: {
        firstName: row.first_name,
        lastName: row.last_name,
        email: row.email,
        phone: row.phone,
        address: row.address,
        city: row.city,
        state: row.state,
        pincode: row.pincode
      },
      payment: {
        razorpayOrderId: row.razorpay_order_id,
        razorpayPaymentId: row.razorpay_payment_id,
        status: row.payment_status,
        method: row.payment_method
      }
    };
  } catch (error) {
    console.error('❌ Failed to get order data:', error);
    throw error;
  }
}

// Send payment success notifications
export async function sendPaymentSuccessNotifications(orderId) {
  try {
    console.log('📢 Sending payment success notifications for order:', orderId);
    
    const { order, customer, payment } = await getOrderDataForNotification(orderId);
    
    const results = {
      email: null,
      sms: null,
      webhook: null
    };

    // Send email notification
    try {
      results.email = await sendPaymentConfirmationEmail(order, customer, payment);
      console.log('✅ Payment confirmation email result:', results.email.success);
    } catch (error) {
      console.error('❌ Email notification failed:', error);
      results.email = { success: false, error: error.message };
    }

    // Send SMS notification
    try {
      const smsMessage = `Payment confirmed! Order ${order.orderNumber} for ₹${order.totalAmount} has been successfully processed. Payment ID: ${payment.razorpayPaymentId}. Thank you for shopping with ${process.env.COMPANY_NAME || 'PayShop'}!`;
      results.sms = await sendSMSNotification(customer.phone, smsMessage);
      console.log('✅ SMS notification result:', results.sms.success);
    } catch (error) {
      console.error('❌ SMS notification failed:', error);
      results.sms = { success: false, error: error.message };
    }

    // Send webhook notification
    try {
      const webhookData = {
        event: 'payment.success',
        order: order,
        customer: customer,
        payment: payment,
        timestamp: new Date().toISOString()
      };
      results.webhook = await sendWebhookNotification(process.env.EXTERNAL_WEBHOOK_URL, webhookData);
      console.log('✅ Webhook notification result:', results.webhook.success);
    } catch (error) {
      console.error('❌ Webhook notification failed:', error);
      results.webhook = { success: false, error: error.message };
    }

    return {
      success: true,
      results: results,
      message: 'Payment success notifications processed'
    };

  } catch (error) {
    console.error('❌ Failed to send payment success notifications:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Send payment failure notifications
export async function sendPaymentFailureNotifications(orderId) {
  try {
    console.log('📢 Sending payment failure notifications for order:', orderId);
    
    const { order, customer, payment } = await getOrderDataForNotification(orderId);
    
    const results = {
      email: null,
      sms: null,
      webhook: null
    };

    // Send email notification
    try {
      results.email = await sendPaymentFailedEmail(order, customer, payment);
      console.log('✅ Payment failed email result:', results.email.success);
    } catch (error) {
      console.error('❌ Email notification failed:', error);
      results.email = { success: false, error: error.message };
    }

    // Send SMS notification
    try {
      const smsMessage = `Payment failed for order ${order.orderNumber}. Please try again or contact support at ${process.env.COMPANY_PHONE || '+91-**********'}. No charges have been made to your account.`;
      results.sms = await sendSMSNotification(customer.phone, smsMessage);
      console.log('✅ SMS notification result:', results.sms.success);
    } catch (error) {
      console.error('❌ SMS notification failed:', error);
      results.sms = { success: false, error: error.message };
    }

    // Send webhook notification
    try {
      const webhookData = {
        event: 'payment.failed',
        order: order,
        customer: customer,
        payment: payment,
        timestamp: new Date().toISOString()
      };
      results.webhook = await sendWebhookNotification(process.env.EXTERNAL_WEBHOOK_URL, webhookData);
      console.log('✅ Webhook notification result:', results.webhook.success);
    } catch (error) {
      console.error('❌ Webhook notification failed:', error);
      results.webhook = { success: false, error: error.message };
    }

    return {
      success: true,
      results: results,
      message: 'Payment failure notifications processed'
    };

  } catch (error) {
    console.error('❌ Failed to send payment failure notifications:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Send order receipt
export async function sendOrderReceipt(orderId) {
  try {
    console.log('📧 Sending order receipt for order:', orderId);
    
    const { order, customer, payment } = await getOrderDataForNotification(orderId);
    
    const result = await sendOrderReceiptEmail(order, customer, payment);
    console.log('✅ Order receipt result:', result.success);
    
    return result;

  } catch (error) {
    console.error('❌ Failed to send order receipt:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Log notification activity
async function logNotificationActivity(orderId, type, status, details) {
  try {
    await pool.query(`
      INSERT INTO notification_logs (order_id, type, status, details, created_at)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
    `, [orderId, type, status, JSON.stringify(details)]);
  } catch (error) {
    console.error('❌ Failed to log notification activity:', error);
  }
}

export default {
  sendPaymentSuccessNotifications,
  sendPaymentFailureNotifications,
  sendOrderReceipt,
  sendSMSNotification,
  sendWebhookNotification
};
