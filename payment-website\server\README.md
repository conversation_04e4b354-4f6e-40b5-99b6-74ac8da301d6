# PayShop Backend - Payment Gateway with Confirmation System

A comprehensive Node.js backend for payment processing using Razorpay with advanced payment confirmation features including email notifications, SMS alerts, and webhook integrations.

## 🚀 Features

### Core Payment Features
- **Razorpay Integration**: Complete payment processing with UPI, cards, and wallets
- **QR Code Generation**: Dynamic UPI QR codes for seamless payments
- **Payment Verification**: Secure signature verification for all transactions
- **Webhook Support**: Real-time payment status updates
- **Order Management**: Complete order lifecycle management

### Enhanced Confirmation System
- **Email Notifications**: Automated payment confirmation and failure emails
- **SMS Notifications**: Optional SMS alerts for payment status
- **Order Receipts**: Professional PDF-style email receipts
- **Retry Mechanisms**: Automatic retry for failed notifications
- **Notification Logging**: Complete audit trail of all notifications

### Security & Performance
- **Rate Limiting**: Protection against API abuse
- **CORS Configuration**: Secure cross-origin requests
- **Input Validation**: Comprehensive data validation with Joi
- **Database Transactions**: ACID compliance for all operations
- **Error Handling**: Robust error handling and logging

## 📋 Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- Razorpay Account (for payment processing)
- Gmail Account (for email notifications) - Optional

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd payment-website/server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=payshop_db
   DB_USER=postgres
   DB_PASSWORD=your_password

   # Razorpay Configuration
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

   # Email Configuration
   EMAIL_SERVICE=gmail
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your_app_password
   EMAIL_FROM=PayShop <<EMAIL>>

   # Notification Settings
   ENABLE_EMAIL_NOTIFICATIONS=true
   ENABLE_SMS_NOTIFICATIONS=false
   ```

4. **Set up PostgreSQL database**
   ```bash
   # Create database
   createdb payshop_db
   
   # Run migrations (tables will be created automatically on first run)
   npm run migrate
   ```

5. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 📚 API Documentation

### Payment Endpoints

#### Create Payment Order
```http
POST /api/payments/create-order
Content-Type: application/json

{
  "customerData": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "**********",
    "address": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001"
  },
  "orderData": {
    "amount": 1000.00,
    "currency": "INR"
  },
  "items": [
    {
      "id": 1,
      "name": "Product Name",
      "price": 1000.00,
      "quantity": 1
    }
  ]
}
```

#### Verify Payment
```http
POST /api/payments/verify
Content-Type: application/json

{
  "razorpay_order_id": "order_xyz",
  "razorpay_payment_id": "pay_abc",
  "razorpay_signature": "signature_hash"
}
```

#### Get Payment Status
```http
GET /api/payments/status/:orderId
```

#### Send Order Receipt
```http
POST /api/payments/send-receipt/:orderId
```

#### Retry Notifications
```http
POST /api/payments/retry-notifications/:orderId
Content-Type: application/json

{
  "type": "success" // or "failure"
}
```

### Order Endpoints

#### Get Order Details
```http
GET /api/orders/:orderId
```

#### Get Customer Orders
```http
GET /api/orders/customer/:email?page=1&limit=10
```

#### Update Order Status
```http
PATCH /api/orders/:orderId/status
Content-Type: application/json

{
  "status": "paid" // pending, paid, failed, cancelled, refunded
}
```

#### Get Order Statistics
```http
GET /api/orders/stats/summary
```

### Webhook Endpoints

#### Razorpay Webhook
```http
POST /api/webhooks/razorpay
X-Razorpay-Signature: webhook_signature
Content-Type: application/json

{
  "event": "payment.captured",
  "payload": {
    // Razorpay webhook payload
  }
}
```

## 🔧 Configuration

### Email Setup (Gmail)

1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the generated password in `EMAIL_PASSWORD`

### Razorpay Setup

1. Create a Razorpay account at https://razorpay.com
2. Get your API keys from the dashboard
3. Set up webhooks:
   - URL: `https://yourdomain.com/api/webhooks/razorpay`
   - Events: `payment.captured`, `payment.failed`, `order.paid`

## 🗄️ Database Schema

### Tables
- **customers**: Customer information
- **orders**: Order details and status
- **payments**: Payment transactions and Razorpay data
- **notification_logs**: Audit trail for all notifications

### Key Relationships
- `orders.customer_id` → `customers.id`
- `payments.order_id` → `orders.id`
- `notification_logs.order_id` → `orders.id`

## 📧 Email Templates

The system includes professional HTML email templates:

- **Payment Confirmation**: Sent when payment is successful
- **Payment Failed**: Sent when payment fails
- **Order Receipt**: Detailed receipt with itemized billing

Templates are located in `/templates/email/` and use Handlebars for dynamic content.

## 🔍 Monitoring & Logging

### Health Check
```http
GET /health
```

### Webhook Health Check
```http
GET /api/webhooks/health
```

### Logs
- All payment activities are logged with timestamps
- Notification attempts are tracked in the database
- Error logs include stack traces in development mode

## 🚀 Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3001
DB_SSL=true
FRONTEND_URL=https://yourdomain.com
```

### Docker Support
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## 🔒 Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Helmet**: Security headers for Express
- **Input Validation**: Joi schema validation
- **SQL Injection Protection**: Parameterized queries
- **CORS**: Configurable cross-origin policies

## 🧪 Testing

```bash
# Test email configuration
curl -X POST http://localhost:3001/api/payments/send-receipt/1

# Test webhook endpoint
curl -X GET http://localhost:3001/api/webhooks/health

# Test database connection
curl -X GET http://localhost:3001/health
```

## 📞 Support

For issues and questions:
- Email: <EMAIL>
- Phone: +91-**********

## 📄 License

MIT License - see LICENSE file for details.
