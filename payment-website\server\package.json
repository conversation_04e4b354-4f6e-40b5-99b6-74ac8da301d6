{"name": "payshop-server", "version": "1.0.0", "description": "Backend server for PayShop with Razorpay integration", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "migrate": "node scripts/migrate.js", "test-email": "node scripts/test-email.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "razorpay": "^2.9.2", "pg": "^8.11.3", "qrcode": "^1.5.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "nodemailer": "^6.9.7", "handlebars": "^4.7.8", "axios": "^1.6.2", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["razorpay", "payment", "ecommerce", "nodejs", "express"], "author": "PayShop", "license": "MIT"}