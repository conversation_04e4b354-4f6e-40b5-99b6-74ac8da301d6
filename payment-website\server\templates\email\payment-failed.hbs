<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #f44336;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #f44336;
            margin: 0;
            font-size: 28px;
        }
        .error-icon {
            font-size: 48px;
            color: #f44336;
            margin-bottom: 10px;
        }
        .order-details {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ff9800;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #f44336;
        }
        .retry-section {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        .contact-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
        }
        .reasons {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .reasons ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .reasons li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="error-icon">❌</div>
            <h1>Payment Failed</h1>
            <p>We're sorry, {{customerName}}</p>
        </div>

        <p>We were unable to process your payment for the following order. Don't worry - no charges have been made to your account.</p>

        <div class="order-details">
            <h3>Order Details</h3>
            <div class="detail-row">
                <span>Order Number:</span>
                <span><strong>{{orderNumber}}</strong></span>
            </div>
            <div class="detail-row">
                <span>Order Date:</span>
                <span>{{orderDate}}</span>
            </div>
            <div class="detail-row">
                <span>Amount:</span>
                <span>{{currency}} {{totalAmount}}</span>
            </div>
        </div>

        <div class="reasons">
            <h4>Common reasons for payment failure:</h4>
            <ul>
                <li>Insufficient funds in your account</li>
                <li>Incorrect card details or expired card</li>
                <li>Network connectivity issues</li>
                <li>Bank security restrictions</li>
                <li>Daily transaction limit exceeded</li>
            </ul>
        </div>

        <div class="retry-section">
            <h3>What's Next?</h3>
            <p>You can try making the payment again or contact your bank if the issue persists.</p>
            <a href="#" class="btn">Retry Payment</a>
            <a href="#" class="btn btn-secondary">View Order Details</a>
        </div>

        <div class="contact-info">
            <h4>Need Help?</h4>
            <p>Our support team is here to help you complete your purchase:</p>
            <p>
                📧 Email: {{companyEmail}}<br>
                📞 Phone: {{companyPhone}}
            </p>
            <p><strong>Support Hours:</strong> Monday to Friday, 9:00 AM - 6:00 PM IST</p>
        </div>

        <div class="footer">
            <p>This is an automated email from {{companyName}}. Please do not reply to this email.</p>
            <p>© {{year}} {{companyName}}. All rights reserved.</p>
        </div>
    </div>

    <script>
        Handlebars.registerHelper('year', function() {
            return new Date().getFullYear();
        });
    </script>
</body>
</html>
