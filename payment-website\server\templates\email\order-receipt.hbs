<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Receipt - {{orderNumber}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2196F3;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2196F3;
            margin: 0;
            font-size: 28px;
        }
        .receipt-icon {
            font-size: 48px;
            color: #2196F3;
            margin-bottom: 10px;
        }
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            gap: 20px;
        }
        .billing-info, .shipping-info {
            flex: 1;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .order-details {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #2196F3;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .items-table th {
            background-color: #2196F3;
            color: white;
            font-weight: bold;
        }
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .total-section {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .total-final {
            font-size: 20px;
            font-weight: bold;
            color: #4CAF50;
            border-top: 2px solid #4CAF50;
            padding-top: 10px;
            margin-top: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
        }
        .contact-info {
            background-color: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .print-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        @media print {
            .print-btn { display: none; }
            body { background-color: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="receipt-icon">🧾</div>
            <h1>Order Receipt</h1>
            <p>Thank you for your purchase!</p>
        </div>

        <div class="company-info">
            <h2>{{companyName}}</h2>
            <p>{{companyAddress}}</p>
            <p>Email: {{companyEmail}} | Phone: {{companyPhone}}</p>
        </div>

        <div class="billing-section">
            <div class="billing-info">
                <h4>Bill To:</h4>
                <p><strong>{{customerName}}</strong></p>
                <p>{{customerAddress.address}}</p>
                <p>{{customerAddress.city}}, {{customerAddress.state}} {{customerAddress.pincode}}</p>
            </div>
            <div class="shipping-info">
                <h4>Order Information:</h4>
                <p><strong>Order #:</strong> {{orderNumber}}</p>
                <p><strong>Order Date:</strong> {{orderDate}}</p>
                <p><strong>Payment ID:</strong> {{paymentId}}</p>
                <p><strong>Payment Method:</strong> {{paymentMethod}}</p>
            </div>
        </div>

        <h3>Items Purchased</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item Description</th>
                    <th>Qty</th>
                    <th>Unit Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{this.name}}</td>
                    <td>{{this.quantity}}</td>
                    <td>₹{{this.price}}</td>
                    <td>₹{{multiply this.price this.quantity}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="total-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>₹{{totalAmount}}</span>
            </div>
            <div class="total-row">
                <span>Tax (0%):</span>
                <span>₹0.00</span>
            </div>
            <div class="total-row">
                <span>Shipping:</span>
                <span>Free</span>
            </div>
            <div class="total-row total-final">
                <span>Total Amount:</span>
                <span>₹{{totalAmount}}</span>
            </div>
        </div>

        <div class="contact-info">
            <h4>Questions about your order?</h4>
            <p>Contact our customer support team:</p>
            <p>
                📧 Email: {{companyEmail}}<br>
                📞 Phone: {{companyPhone}}
            </p>
        </div>

        <div style="text-align: center;">
            <button class="print-btn" onclick="window.print()">Print Receipt</button>
        </div>

        <div class="footer">
            <p>This is your official receipt for order {{orderNumber}}.</p>
            <p>© {{year}} {{companyName}}. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Helper function for Handlebars
        Handlebars.registerHelper('multiply', function(a, b) {
            return a * b;
        });
        
        Handlebars.registerHelper('year', function() {
            return new Date().getFullYear();
        });
    </script>
</body>
</html>
